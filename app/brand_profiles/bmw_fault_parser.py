"""
BMW Fault Code Parser
Parses BMW-specific fault codes from bimmer-tool reference data
"""
import csv
import os
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class BMWFaultCode:
    """BMW-specific fault code information"""
    code: str
    description: str
    ecu_module: str
    severity: str = "medium"
    possible_causes: List[str] = None
    repair_hints: List[str] = None
    
    def __post_init__(self):
        if self.possible_causes is None:
            self.possible_causes = []
        if self.repair_hints is None:
            self.repair_hints = []


class BMWFaultCodeParser:
    """
    Parser for BMW fault codes from bimmer-tool reference data
    """
    
    def __init__(self):
        self.fault_codes: Dict[str, BMWFaultCode] = {}
        self.ecu_modules: Dict[str, str] = {}
        self.references_path = Path(__file__).parent.parent.parent / "references" / "bimmer-tool_3.7.8_full" / "fault-codes"
        self._load_ecu_mappings()
        self._load_fault_codes()
    
    def _load_ecu_mappings(self):
        """Load ECU module mappings"""
        self.ecu_modules = {
            # Engine Management
            "DME": "Digital Motor Electronics",
            "DDE": "Digital Diesel Electronics", 
            "MSD": "Motor Sport Development",
            "MSS": "Motor Sport System",
            "MEV": "Motor Electronics Valvetronic",
            "MED": "Motor Electronics Diesel",
            "MEVD": "Motor Electronics Valvetronic Diesel",
            
            # Transmission
            "EGS": "Electronic Gearbox System",
            "DKG": "Dual Clutch Gearbox",
            "SMG": "Sequential Manual Gearbox",
            
            # Chassis Systems
            "DSC": "Dynamic Stability Control",
            "EHC": "Electronic Height Control",
            "EPS": "Electric Power Steering",
            "PDC": "Park Distance Control",
            
            # Body Electronics
            "FRM": "Footwell Module",
            "BDC": "Body Domain Controller",
            "CAS": "Car Access System",
            "ZGW": "Central Gateway",
            "KOMBI": "Instrument Cluster",
            
            # Comfort Systems
            "CCC": "Car Communication Computer",
            "CIC": "Car Information Computer",
            "NBT": "Next Big Thing",
            "CHAMP": "Telematics Control Unit",
            
            # Safety Systems
            "ACSM": "Airbag Control and Sensing Module",
            "MRS": "Multiple Restraint System",
            "RDC": "Tire Pressure Control",
            
            # Lighting
            "LCM": "Light Control Module",
            "FRM": "Footwell Module",
            "LM": "Light Module",
            
            # Climate
            "IHKA": "Integrated Heating/AC",
            "IHKR": "Integrated Heating/AC Rear",
        }
    
    def _load_fault_codes(self):
        """Load fault codes from CSV files"""
        if not self.references_path.exists():
            logger.warning(f"BMW fault codes path not found: {self.references_path}")
            return
        
        csv_files = list(self.references_path.glob("*.csv"))
        logger.info(f"Found {len(csv_files)} BMW fault code files")
        
        for csv_file in csv_files:
            try:
                self._parse_csv_file(csv_file)
            except Exception as e:
                logger.error(f"Error parsing {csv_file.name}: {e}")
    
    def _parse_csv_file(self, csv_file: Path):
        """Parse individual CSV file"""
        ecu_name = csv_file.stem.split('_')[0]  # Extract ECU name from filename
        
        try:
            with open(csv_file, 'r', encoding='utf-8', errors='ignore') as f:
                # Try to detect delimiter
                sample = f.read(1024)
                f.seek(0)
                
                delimiter = ';' if ';' in sample else ','
                reader = csv.reader(f, delimiter=delimiter)
                
                for row_num, row in enumerate(reader):
                    if len(row) >= 2 and row[0] and not row[0].startswith('#'):
                        try:
                            fault_code = self._parse_fault_code_row(row, ecu_name)
                            if fault_code:
                                self.fault_codes[fault_code.code] = fault_code
                        except Exception as e:
                            logger.debug(f"Error parsing row {row_num} in {csv_file.name}: {e}")
                            
        except Exception as e:
            logger.error(f"Error reading {csv_file}: {e}")
    
    def _parse_fault_code_row(self, row: List[str], ecu_name: str) -> Optional[BMWFaultCode]:
        """Parse individual fault code row"""
        if len(row) < 2:
            return None
        
        code = row[0].strip()
        description = row[1].strip()
        
        if not code or not description:
            return None
        
        # Determine severity based on description keywords
        severity = self._determine_severity(description)
        
        # Extract possible causes and repair hints from description
        possible_causes, repair_hints = self._extract_diagnostic_info(description)
        
        return BMWFaultCode(
            code=code,
            description=description,
            ecu_module=self.ecu_modules.get(ecu_name, ecu_name),
            severity=severity,
            possible_causes=possible_causes,
            repair_hints=repair_hints
        )
    
    def _determine_severity(self, description: str) -> str:
        """Determine fault severity from description"""
        description_lower = description.lower()
        
        # High severity keywords
        high_keywords = [
            'engine', 'airbag', 'brake', 'steering', 'safety', 'critical',
            'emergency', 'stop', 'failure', 'malfunction', 'defective'
        ]
        
        # Low severity keywords  
        low_keywords = [
            'information', 'warning', 'reminder', 'service', 'maintenance',
            'check', 'monitor', 'status', 'notification'
        ]
        
        if any(keyword in description_lower for keyword in high_keywords):
            return "high"
        elif any(keyword in description_lower for keyword in low_keywords):
            return "low"
        else:
            return "medium"
    
    def _extract_diagnostic_info(self, description: str) -> tuple[List[str], List[str]]:
        """Extract possible causes and repair hints from description"""
        possible_causes = []
        repair_hints = []
        
        # Common BMW fault patterns
        if "sensor" in description.lower():
            possible_causes.append("Faulty sensor")
            repair_hints.append("Check sensor wiring and connections")
        
        if "wiring" in description.lower() or "wire" in description.lower():
            possible_causes.append("Wiring issue")
            repair_hints.append("Inspect wiring harness for damage")
        
        if "module" in description.lower() or "control unit" in description.lower():
            possible_causes.append("Control module fault")
            repair_hints.append("Check module programming and connections")
        
        if "voltage" in description.lower():
            possible_causes.append("Voltage supply issue")
            repair_hints.append("Check power supply and ground connections")
        
        return possible_causes, repair_hints
    
    def get_fault_code(self, code: str) -> Optional[BMWFaultCode]:
        """Get fault code information"""
        return self.fault_codes.get(code)
    
    def search_fault_codes(self, query: str) -> List[BMWFaultCode]:
        """Search fault codes by description"""
        query_lower = query.lower()
        results = []
        
        for fault_code in self.fault_codes.values():
            if (query_lower in fault_code.description.lower() or 
                query_lower in fault_code.ecu_module.lower()):
                results.append(fault_code)
        
        return results
    
    def get_codes_by_ecu(self, ecu_name: str) -> List[BMWFaultCode]:
        """Get all fault codes for specific ECU"""
        results = []
        ecu_full_name = self.ecu_modules.get(ecu_name, ecu_name)
        
        for fault_code in self.fault_codes.values():
            if fault_code.ecu_module == ecu_full_name:
                results.append(fault_code)
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get parser statistics"""
        ecu_counts = {}
        severity_counts = {"high": 0, "medium": 0, "low": 0}
        
        for fault_code in self.fault_codes.values():
            ecu_counts[fault_code.ecu_module] = ecu_counts.get(fault_code.ecu_module, 0) + 1
            severity_counts[fault_code.severity] += 1
        
        return {
            "total_codes": len(self.fault_codes),
            "ecu_distribution": ecu_counts,
            "severity_distribution": severity_counts,
            "supported_ecus": list(self.ecu_modules.keys())
        }
