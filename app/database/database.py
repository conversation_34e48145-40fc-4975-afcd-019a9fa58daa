"""
Database connection and session management
"""
import logging
from typing import Generator, Optional, List, Dict, Any
try:
    from sqlalchemy import create_engine, text
    from sqlalchemy.orm import sessionmaker, Session
    from sqlalchemy.exc import SQLAlchemyError
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    print("Warning: SQLAlchemy not available. Install with: pip install sqlalchemy")
    SQLALCHEMY_AVAILABLE = False
    # Mock classes for development
    def create_engine(*args, **kwargs):
        return type('MockEngine', (), {
            'connect': lambda: type('MockConnection', (), {
                'execute': lambda self, query: type('MockResult', (), {
                    'fetchall': lambda: [],
                    'fetchone': lambda: None
                })(),
                'close': lambda: None
            })()
        })()

    def sessionmaker(*args, **kwargs):
        return lambda: type('MockSession', (), {
            'query': lambda self, model: type('<PERSON>ckQuery', (), {
                'filter': lambda self, *args: self,
                'all': lambda: [],
                'first': lambda: None
            })(),
            'add': lambda self, obj: None,
            'commit': lambda self: None,
            'rollback': lambda self: None,
            'close': lambda self: None
        })()

    Session = type('MockSession', (), {})()
    SQLAlchemyError = Exception
    text = lambda x: x
from contextlib import contextmanager

from .models import Base, DTCCode, VehicleProfile, DiagnosticSession, ECUParameter, BrandProfile
from ..config import settings


logger = logging.getLogger(__name__)


class DatabaseManager:
    """Database connection and session manager"""
    
    def __init__(self, database_url: Optional[str] = None):
        self.database_url = database_url or settings.database_url
        self.engine = None
        self.SessionLocal = None
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize database connection"""
        try:
            self.engine = create_engine(
                self.database_url,
                echo=settings.debug,  # Log SQL queries in debug mode
                pool_pre_ping=True,   # Verify connections before use
                pool_recycle=3600     # Recycle connections every hour
            )
            
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            logger.info(f"Database initialized: {self.database_url}")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def create_tables(self):
        """Create all database tables"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")
            raise
    
    def drop_tables(self):
        """Drop all database tables (use with caution!)"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.warning("All database tables dropped")
        except Exception as e:
            logger.error(f"Failed to drop database tables: {e}")
            raise
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """Get database session with automatic cleanup"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def get_session_sync(self) -> Session:
        """Get database session for synchronous use"""
        return self.SessionLocal()
    
    def health_check(self) -> bool:
        """Check database connection health"""
        try:
            with self.get_session() as session:
                session.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False


class DTCRepository:
    """Repository for DTC code operations"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def get_dtc_by_code(self, code: str) -> Optional[DTCCode]:
        """Get DTC by code"""
        with self.db_manager.get_session() as session:
            return session.query(DTCCode).filter(DTCCode.code == code.upper()).first()
    
    def search_dtcs(self, 
                   search_term: str = None,
                   category: str = None,
                   severity: str = None,
                   system: str = None,
                   limit: int = 100) -> List[DTCCode]:
        """Search DTCs with filters"""
        with self.db_manager.get_session() as session:
            query = session.query(DTCCode)
            
            if search_term:
                search_pattern = f"%{search_term}%"
                query = query.filter(
                    (DTCCode.code.ilike(search_pattern)) |
                    (DTCCode.description.ilike(search_pattern))
                )
            
            if category:
                query = query.filter(DTCCode.category == category)
            
            if severity:
                query = query.filter(DTCCode.severity == severity)
            
            if system:
                query = query.filter(DTCCode.system == system)
            
            return query.limit(limit).all()
    
    def create_dtc(self, dtc_data: Dict[str, Any]) -> DTCCode:
        """Create new DTC record"""
        with self.db_manager.get_session() as session:
            dtc = DTCCode(**dtc_data)
            session.add(dtc)
            session.flush()
            session.refresh(dtc)
            return dtc
    
    def update_dtc(self, code: str, update_data: Dict[str, Any]) -> Optional[DTCCode]:
        """Update existing DTC record"""
        with self.db_manager.get_session() as session:
            dtc = session.query(DTCCode).filter(DTCCode.code == code.upper()).first()
            if dtc:
                for key, value in update_data.items():
                    if hasattr(dtc, key):
                        setattr(dtc, key, value)
                session.flush()
                session.refresh(dtc)
            return dtc
    
    def get_dtcs_by_category(self, category: str) -> List[DTCCode]:
        """Get all DTCs in a category"""
        with self.db_manager.get_session() as session:
            return session.query(DTCCode).filter(DTCCode.category == category).all()
    
    def get_dtcs_by_severity(self, severity: str) -> List[DTCCode]:
        """Get all DTCs with specific severity"""
        with self.db_manager.get_session() as session:
            return session.query(DTCCode).filter(DTCCode.severity == severity).all()
    
    def bulk_insert_dtcs(self, dtc_list: List[Dict[str, Any]]) -> int:
        """Bulk insert DTC records"""
        with self.db_manager.get_session() as session:
            dtc_objects = [DTCCode(**dtc_data) for dtc_data in dtc_list]
            session.bulk_save_objects(dtc_objects)
            return len(dtc_objects)


class VehicleRepository:
    """Repository for vehicle profile operations"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def get_vehicle_by_vin(self, vin: str) -> Optional[VehicleProfile]:
        """Get vehicle by VIN"""
        with self.db_manager.get_session() as session:
            return session.query(VehicleProfile).filter(VehicleProfile.vin == vin.upper()).first()
    
    def search_vehicles(self,
                       make: str = None,
                       model: str = None,
                       year: int = None,
                       limit: int = 100) -> List[VehicleProfile]:
        """Search vehicles with filters"""
        with self.db_manager.get_session() as session:
            query = session.query(VehicleProfile)
            
            if make:
                query = query.filter(VehicleProfile.make.ilike(f"%{make}%"))
            
            if model:
                query = query.filter(VehicleProfile.model.ilike(f"%{model}%"))
            
            if year:
                query = query.filter(VehicleProfile.year == year)
            
            return query.limit(limit).all()
    
    def create_vehicle(self, vehicle_data: Dict[str, Any]) -> VehicleProfile:
        """Create new vehicle profile"""
        with self.db_manager.get_session() as session:
            vehicle = VehicleProfile(**vehicle_data)
            session.add(vehicle)
            session.flush()
            session.refresh(vehicle)
            return vehicle
    
    def update_vehicle(self, vehicle_id: int, update_data: Dict[str, Any]) -> Optional[VehicleProfile]:
        """Update existing vehicle profile"""
        with self.db_manager.get_session() as session:
            vehicle = session.query(VehicleProfile).filter(VehicleProfile.id == vehicle_id).first()
            if vehicle:
                for key, value in update_data.items():
                    if hasattr(vehicle, key):
                        setattr(vehicle, key, value)
                session.flush()
                session.refresh(vehicle)
            return vehicle


class DiagnosticSessionRepository:
    """Repository for diagnostic session operations"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def create_session(self, session_data: Dict[str, Any]) -> DiagnosticSession:
        """Create new diagnostic session"""
        with self.db_manager.get_session() as session:
            diag_session = DiagnosticSession(**session_data)
            session.add(diag_session)
            session.flush()
            session.refresh(diag_session)
            return diag_session
    
    def get_session_by_id(self, session_id: str) -> Optional[DiagnosticSession]:
        """Get diagnostic session by ID"""
        with self.db_manager.get_session() as session:
            return session.query(DiagnosticSession).filter(
                DiagnosticSession.session_id == session_id
            ).first()
    
    def get_vehicle_sessions(self, vehicle_id: int, limit: int = 50) -> List[DiagnosticSession]:
        """Get diagnostic sessions for a vehicle"""
        with self.db_manager.get_session() as session:
            return session.query(DiagnosticSession).filter(
                DiagnosticSession.vehicle_id == vehicle_id
            ).order_by(DiagnosticSession.start_time.desc()).limit(limit).all()
    
    def update_session(self, session_id: str, update_data: Dict[str, Any]) -> Optional[DiagnosticSession]:
        """Update diagnostic session"""
        with self.db_manager.get_session() as session:
            diag_session = session.query(DiagnosticSession).filter(
                DiagnosticSession.session_id == session_id
            ).first()
            if diag_session:
                for key, value in update_data.items():
                    if hasattr(diag_session, key):
                        setattr(diag_session, key, value)
                session.flush()
                session.refresh(diag_session)
            return diag_session
    
    def get_recent_sessions(self, days: int = 30, limit: int = 100) -> List[DiagnosticSession]:
        """Get recent diagnostic sessions"""
        from datetime import datetime, timedelta
        
        with self.db_manager.get_session() as session:
            cutoff_date = datetime.now() - timedelta(days=days)
            return session.query(DiagnosticSession).filter(
                DiagnosticSession.start_time >= cutoff_date
            ).order_by(DiagnosticSession.start_time.desc()).limit(limit).all()


# Global database manager instance
db_manager = DatabaseManager()

# Repository instances
dtc_repository = DTCRepository(db_manager)
vehicle_repository = VehicleRepository(db_manager)
session_repository = DiagnosticSessionRepository(db_manager)


def init_database():
    """Initialize database and create tables"""
    try:
        db_manager.create_tables()
        logger.info("Database initialization completed")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


def get_db_session() -> Generator[Session, None, None]:
    """Dependency for FastAPI to get database session"""
    with db_manager.get_session() as session:
        yield session
