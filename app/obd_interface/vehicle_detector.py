"""
Vehicle Detection Module
Automatically detects vehicle make, model, and year from OBD connection
"""
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class VehicleInfo:
    """Vehicle information detected from OBD"""
    make: Optional[str] = None
    model: Optional[str] = None
    year: Optional[int] = None
    vin: Optional[str] = None
    engine_type: Optional[str] = None
    fuel_type: Optional[str] = None
    confidence: float = 0.0
    detection_method: str = "unknown"


class VehicleDetector:
    """
    Detects vehicle information from OBD connection
    """
    
    def __init__(self):
        self.vin_patterns = self._build_vin_patterns()
        self.protocol_patterns = self._build_protocol_patterns()
        self.pid_patterns = self._build_pid_patterns()
    
    def _build_vin_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Build VIN decoding patterns"""
        return {
            # World Manufacturer Identifier (WMI) patterns
            "1HG": {"make": "Honda", "region": "USA"},
            "1G1": {"make": "Chevrolet", "region": "USA"},
            "1FA": {"make": "Ford", "region": "USA"},
            "1FT": {"make": "Ford", "region": "USA"},
            "1GC": {"make": "Chevrolet", "region": "USA"},
            "1GM": {"make": "Pontiac", "region": "USA"},
            "1N4": {"make": "Nissan", "region": "USA"},
            "1NX": {"make": "Toyota", "region": "USA"},
            "1VW": {"make": "Volkswagen", "region": "USA"},
            "2HG": {"make": "Honda", "region": "Canada"},
            "2T1": {"make": "Toyota", "region": "Canada"},
            "3FA": {"make": "Ford", "region": "Mexico"},
            "3VW": {"make": "Volkswagen", "region": "Mexico"},
            "4F2": {"make": "Mazda", "region": "USA"},
            "4T1": {"make": "Toyota", "region": "USA"},
            "5NP": {"make": "Hyundai", "region": "USA"},
            "5YJ": {"make": "Tesla", "region": "USA"},
            "JHM": {"make": "Honda", "region": "Japan"},
            "JTD": {"make": "Toyota", "region": "Japan"},
            "JF1": {"make": "Subaru", "region": "Japan"},
            "JN1": {"make": "Nissan", "region": "Japan"},
            "KMH": {"make": "Hyundai", "region": "Korea"},
            "KNA": {"make": "Kia", "region": "Korea"},
            "SAL": {"make": "Land Rover", "region": "UK"},
            "SAJ": {"make": "Jaguar", "region": "UK"},
            "SCC": {"make": "Lotus", "region": "UK"},
            "TRU": {"make": "Audi", "region": "Hungary"},
            "VF1": {"make": "Renault", "region": "France"},
            "VF3": {"make": "Peugeot", "region": "France"},
            "VF7": {"make": "Citroën", "region": "France"},
            "VWV": {"make": "Volkswagen", "region": "Germany"},
            "WAU": {"make": "Audi", "region": "Germany"},
            "WBA": {"make": "BMW", "region": "Germany"},
            "WBS": {"make": "BMW", "region": "Germany"},
            "WDD": {"make": "Mercedes-Benz", "region": "Germany"},
            "WDC": {"make": "Mercedes-Benz", "region": "Germany"},
            "WME": {"make": "Mercedes-Benz", "region": "Germany"},
            "WVW": {"make": "Volkswagen", "region": "Germany"},
            "WP0": {"make": "Porsche", "region": "Germany"},
            "YV1": {"make": "Volvo", "region": "Sweden"},
            "ZAR": {"make": "Alfa Romeo", "region": "Italy"},
            "ZFA": {"make": "Fiat", "region": "Italy"},
            "ZFF": {"make": "Ferrari", "region": "Italy"},
            "ZLA": {"make": "Lancia", "region": "Italy"}
        }
    
    def _build_protocol_patterns(self) -> Dict[str, List[str]]:
        """Build protocol-based detection patterns"""
        return {
            "BMW": ["ISO_14230_4_KWP", "ISO_9141_2"],
            "Mercedes": ["ISO_14230_4_KWP", "ISO_9141_2"],
            "Audi": ["ISO_14230_4_KWP", "ISO_15765_4_CAN"],
            "Volkswagen": ["ISO_14230_4_KWP", "ISO_15765_4_CAN"],
            "Toyota": ["ISO_15765_4_CAN", "ISO_9141_2"],
            "Honda": ["ISO_15765_4_CAN", "ISO_9141_2"],
            "Nissan": ["ISO_15765_4_CAN", "ISO_9141_2"],
            "Ford": ["ISO_15765_4_CAN", "SAE_J1850_PWM"],
            "GM": ["ISO_15765_4_CAN", "SAE_J1850_VPW"],
            "Chrysler": ["ISO_15765_4_CAN", "SAE_J1850_PWM"]
        }
    
    def _build_pid_patterns(self) -> Dict[str, List[str]]:
        """Build PID-based detection patterns"""
        return {
            "BMW": ["0x10", "0x11", "0x12", "0x13"],  # Valvetronic, VANOS PIDs
            "Mercedes": ["0x20", "0x21", "0x22"],     # Mercedes-specific PIDs
            "Audi": ["0x30", "0x31", "0x32"],        # VAG-specific PIDs
            "Volkswagen": ["0x30", "0x31", "0x32"],  # VAG-specific PIDs
            "Toyota": ["0x40", "0x41", "0x42"],      # Toyota-specific PIDs
            "Honda": ["0x50", "0x51", "0x52"],       # Honda-specific PIDs
        }
    
    async def detect_vehicle(self, obd_connection) -> VehicleInfo:
        """
        Detect vehicle information from OBD connection
        """
        vehicle_info = VehicleInfo()
        detection_methods = []
        
        # Method 1: VIN-based detection
        vin_info = await self._detect_from_vin(obd_connection)
        if vin_info.make:
            vehicle_info = vin_info
            detection_methods.append("VIN")
        
        # Method 2: Protocol-based detection
        if not vehicle_info.make:
            protocol_info = await self._detect_from_protocol(obd_connection)
            if protocol_info.make:
                vehicle_info = protocol_info
                detection_methods.append("Protocol")
        
        # Method 3: PID-based detection
        if not vehicle_info.make:
            pid_info = await self._detect_from_pids(obd_connection)
            if pid_info.make:
                vehicle_info = pid_info
                detection_methods.append("PID")
        
        # Method 4: ECU response pattern detection
        if not vehicle_info.make:
            ecu_info = await self._detect_from_ecu_patterns(obd_connection)
            if ecu_info.make:
                vehicle_info = ecu_info
                detection_methods.append("ECU")
        
        vehicle_info.detection_method = "+".join(detection_methods) if detection_methods else "unknown"
        
        logger.info(f"Vehicle detection result: {vehicle_info.make} {vehicle_info.model} "
                   f"({vehicle_info.year}) - Confidence: {vehicle_info.confidence:.2f} "
                   f"- Method: {vehicle_info.detection_method}")
        
        return vehicle_info
    
    async def _detect_from_vin(self, obd_connection) -> VehicleInfo:
        """Detect vehicle from VIN"""
        try:
            import obd
            
            # Try to read VIN
            vin_cmd = obd.commands.VIN
            response = obd_connection.query(vin_cmd)
            
            if response.value:
                vin = str(response.value).strip()
                return self._decode_vin(vin)
        
        except Exception as e:
            logger.debug(f"VIN detection failed: {e}")
        
        return VehicleInfo()
    
    def _decode_vin(self, vin: str) -> VehicleInfo:
        """Decode VIN to extract vehicle information"""
        if len(vin) != 17:
            return VehicleInfo()
        
        # Extract WMI (World Manufacturer Identifier)
        wmi = vin[:3]
        
        # Check for exact match
        if wmi in self.vin_patterns:
            make_info = self.vin_patterns[wmi]
            
            # Extract year from VIN (10th character)
            year_char = vin[9]
            year = self._decode_vin_year(year_char)
            
            return VehicleInfo(
                make=make_info["make"],
                year=year,
                vin=vin,
                confidence=0.95,
                detection_method="VIN"
            )
        
        # Check for partial matches
        for pattern, make_info in self.vin_patterns.items():
            if wmi.startswith(pattern[:2]):
                year_char = vin[9]
                year = self._decode_vin_year(year_char)
                
                return VehicleInfo(
                    make=make_info["make"],
                    year=year,
                    vin=vin,
                    confidence=0.7,
                    detection_method="VIN_partial"
                )
        
        return VehicleInfo(vin=vin, confidence=0.1)
    
    def _decode_vin_year(self, year_char: str) -> Optional[int]:
        """Decode year from VIN character"""
        year_map = {
            'A': 1980, 'B': 1981, 'C': 1982, 'D': 1983, 'E': 1984, 'F': 1985,
            'G': 1986, 'H': 1987, 'J': 1988, 'K': 1989, 'L': 1990, 'M': 1991,
            'N': 1992, 'P': 1993, 'R': 1994, 'S': 1995, 'T': 1996, 'V': 1997,
            'W': 1998, 'X': 1999, 'Y': 2000, '1': 2001, '2': 2002, '3': 2003,
            '4': 2004, '5': 2005, '6': 2006, '7': 2007, '8': 2008, '9': 2009,
            'A': 2010, 'B': 2011, 'C': 2012, 'D': 2013, 'E': 2014, 'F': 2015,
            'G': 2016, 'H': 2017, 'J': 2018, 'K': 2019, 'L': 2020, 'M': 2021,
            'N': 2022, 'P': 2023, 'R': 2024
        }
        
        return year_map.get(year_char.upper())
    
    async def _detect_from_protocol(self, obd_connection) -> VehicleInfo:
        """Detect vehicle from OBD protocol"""
        try:
            # Get protocol information
            protocol = getattr(obd_connection, 'protocol', None)
            if protocol:
                protocol_name = str(protocol).upper()
                
                for make, protocols in self.protocol_patterns.items():
                    if any(p in protocol_name for p in protocols):
                        return VehicleInfo(
                            make=make,
                            confidence=0.3,
                            detection_method="Protocol"
                        )
        
        except Exception as e:
            logger.debug(f"Protocol detection failed: {e}")
        
        return VehicleInfo()
    
    async def _detect_from_pids(self, obd_connection) -> VehicleInfo:
        """Detect vehicle from supported PIDs"""
        try:
            import obd
            
            # Test brand-specific PIDs
            for make, pids in self.pid_patterns.items():
                supported_count = 0
                
                for pid in pids:
                    try:
                        cmd = obd.commands.has_pid(pid)
                        if cmd:
                            response = obd_connection.query(cmd)
                            if response.value is not None:
                                supported_count += 1
                    except:
                        continue
                
                if supported_count > 0:
                    confidence = min(0.8, supported_count / len(pids))
                    return VehicleInfo(
                        make=make,
                        confidence=confidence,
                        detection_method="PID"
                    )
        
        except Exception as e:
            logger.debug(f"PID detection failed: {e}")
        
        return VehicleInfo()
    
    async def _detect_from_ecu_patterns(self, obd_connection) -> VehicleInfo:
        """Detect vehicle from ECU response patterns"""
        try:
            import obd
            
            # Test basic OBD commands and analyze responses
            test_commands = [
                obd.commands.RPM,
                obd.commands.SPEED,
                obd.commands.COOLANT_TEMP
            ]
            
            response_patterns = []
            
            for cmd in test_commands:
                try:
                    response = obd_connection.query(cmd)
                    if response.raw:
                        # Analyze raw response for manufacturer patterns
                        raw_hex = response.raw.hex() if hasattr(response.raw, 'hex') else str(response.raw)
                        response_patterns.append(raw_hex)
                except:
                    continue
            
            # Analyze patterns for manufacturer signatures
            if response_patterns:
                # BMW typically has specific response patterns
                if any("41" in pattern and len(pattern) >= 8 for pattern in response_patterns):
                    return VehicleInfo(
                        make="BMW",
                        confidence=0.4,
                        detection_method="ECU_pattern"
                    )
        
        except Exception as e:
            logger.debug(f"ECU pattern detection failed: {e}")
        
        return VehicleInfo()
    
    def enhance_vehicle_info(self, vehicle_info: VehicleInfo, additional_data: Dict[str, Any]) -> VehicleInfo:
        """Enhance vehicle info with additional data"""
        if "engine_displacement" in additional_data:
            vehicle_info.engine_type = f"{additional_data['engine_displacement']}L"
        
        if "fuel_type" in additional_data:
            vehicle_info.fuel_type = additional_data["fuel_type"]
        
        return vehicle_info
