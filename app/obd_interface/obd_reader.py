"""
OBD2 Reader implementation using python-OBD library
Enhanced with modular architecture and protocol detection
Supports ELM327 adapters via Bluetooth and USB
"""
import asyncio
import logging
from typing import List, Dict, Optional, Any, Union
from dataclasses import dataclass
from datetime import datetime

try:
    import obd
    from obd import OBDStatus
    import serial
    import serial.tools.list_ports
    OBD_AVAILABLE = True
except ImportError:
    print("Warning: OBD modules not available. Install with: pip install obd pyserial")
    OBD_AVAILABLE = False
    # Mock classes for development
    class MockOBD:
        def __init__(self, *args, **kwargs):
            pass
        def status(self):
            return "CAR_CONNECTED"
        def is_connected(self):
            return True
        def query(self, cmd):
            class MockResponse:
                value = None
                raw = None
                def is_null(self):
                    return False
            return MockResponse()
        def close(self):
            pass
        def port_name(self):
            return "mock_port"
        def protocol_name(self):
            return "mock_protocol"
        @property
        def supported_commands(self):
            return []

    obd = type('MockOBDModule', (), {
        'OBD': MockOBD,
        'commands': type('MockCommands', (), {
            'GET_DTC': 'mock_dtc',
            'GET_PENDING_DTC': 'mock_pending',
            'GET_PERMANENT_DTC': 'mock_permanent',
            'CLEAR_DTC': 'mock_clear',
            'VIN': 'mock_vin',
            'ELM_ECU_NAME': 'mock_ecu',
            'GET_SUPPORTED_PIDS': 'mock_pids',
            'GET_FREEZE_DTC': 'mock_freeze',
            'RPM': 'mock_rpm',
            'SPEED': 'mock_speed',
            'COOLANT_TEMP': 'mock_temp',
            'has_pid': lambda x: None
        })()
    })()

    OBDStatus = type('MockOBDStatus', (), {
        'CAR_CONNECTED': 'CAR_CONNECTED'
    })()

    serial = type('MockSerial', (), {
        'tools': type('MockTools', (), {
            'list_ports': type('MockListPorts', (), {
                'comports': lambda: []
            })()
        })()
    })()

from ..config import settings, OBDConfig
from .protocol_detector import ProtocolManager, StandardProtocolDetector, OBDProtocol
from .command_database import CommandDatabase
from .response_decoder import ResponseProcessor
from .vehicle_detector import VehicleDetector, VehicleInfo


logger = logging.getLogger(__name__)


@dataclass
class DTCResult:
    """Data class for DTC (Diagnostic Trouble Code) results"""
    code: str
    description: str
    status: str
    timestamp: datetime
    freeze_frame_data: Optional[Dict[str, Any]] = None

    def dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'code': self.code,
            'description': self.description,
            'status': self.status,
            'timestamp': self.timestamp.isoformat() if isinstance(self.timestamp, datetime) else self.timestamp,
            'freeze_frame_data': self.freeze_frame_data
        }


@dataclass
class OBDParameter:
    """Data class for OBD parameter readings"""
    pid: str
    name: str
    value: Any
    unit: str
    timestamp: datetime

    def dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'pid': self.pid,
            'name': self.name,
            'value': self.value,
            'unit': self.unit,
            'timestamp': self.timestamp.isoformat() if isinstance(self.timestamp, datetime) else self.timestamp
        }


class OBDReader:
    """
    OBD2 Reader class for communicating with ELM327 adapters
    """
    
    def __init__(self, port: Optional[str] = None, baudrate: int = 38400):
        self.port = port or settings.obd_port
        self.baudrate = baudrate
        self.connection: Optional[obd.OBD] = None
        self.is_connected = False

        # Initialize modular components
        self.protocol_manager = ProtocolManager(StandardProtocolDetector())
        self.command_database = CommandDatabase()
        self.response_processor = ResponseProcessor(self.command_database)
        self.vehicle_detector = VehicleDetector()

        # Vehicle information
        self.detected_vehicle: Optional[VehicleInfo] = None
        
    async def connect(self) -> bool:
        """
        Establish connection to OBD2 adapter with protocol detection
        """
        try:
            if self.port:
                # Connect to specific port
                self.connection = obd.OBD(self.port, baudrate=self.baudrate)
            else:
                # Auto-detect port
                self.connection = obd.OBD()

            self.is_connected = self.connection.status() == OBDStatus.CAR_CONNECTED

            if self.is_connected:
                # Initialize protocol detection
                protocol_initialized = await self.protocol_manager.initialize_protocol(self.connection)

                if protocol_initialized:
                    current_protocol = self.protocol_manager.get_current_protocol()
                    logger.info(f"Successfully connected to OBD2 adapter on {self.connection.port_name()}")
                    logger.info(f"Protocol detected: {current_protocol.value if current_protocol else 'Unknown'}")

                    # Detect vehicle information
                    try:
                        self.detected_vehicle = await self.vehicle_detector.detect_vehicle(self.connection)
                        if self.detected_vehicle.make:
                            logger.info(f"Detected vehicle: {self.detected_vehicle.make} {self.detected_vehicle.model or ''} "
                                       f"({self.detected_vehicle.year or 'Unknown year'})")
                    except Exception as e:
                        logger.warning(f"Vehicle detection failed: {e}")

                    return True
                else:
                    logger.warning("Connected but protocol detection failed")
                    return True  # Still allow connection for basic functionality
            else:
                logger.error(f"Failed to connect to vehicle. Status: {self.connection.status()}")
                return False

        except Exception as e:
            logger.error(f"Error connecting to OBD2 adapter: {e}")
            return False
    
    async def disconnect(self):
        """
        Close OBD2 connection
        """
        if self.connection:
            self.connection.close()
            self.is_connected = False
            logger.info("OBD2 connection closed")
    
    @staticmethod
    def list_available_ports() -> List[str]:
        """
        List all available serial ports
        """
        ports = serial.tools.list_ports.comports()
        return [port.device for port in ports]
    
    async def get_supported_pids(self) -> List[str]:
        """
        Get list of supported PIDs from the vehicle
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")
        
        supported_pids = []
        try:
            # Query supported PIDs
            cmd = obd.commands.GET_SUPPORTED_PIDS
            response = self.connection.query(cmd)
            
            if response.value:
                supported_pids = [str(pid) for pid in response.value]
                
        except Exception as e:
            logger.error(f"Error getting supported PIDs: {e}")
        
        return supported_pids
    
    async def read_dtcs(self) -> List[DTCResult]:
        """
        Read Diagnostic Trouble Codes from the vehicle with enhanced processing
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")

        dtcs = []
        try:
            # Read stored DTCs
            cmd = obd.commands.GET_DTC
            response = self.connection.query(cmd)

            if response.value:
                for dtc_tuple in response.value:
                    code, description = dtc_tuple
                    dtc = DTCResult(
                        code=code,
                        description=description,
                        status="stored",
                        timestamp=datetime.now()
                    )
                    dtcs.append(dtc)

            # Read pending DTCs
            try:
                pending_cmd = obd.commands.GET_PENDING_DTC
                pending_response = self.connection.query(pending_cmd)

                if pending_response.value:
                    for dtc_tuple in pending_response.value:
                        code, description = dtc_tuple
                        dtc = DTCResult(
                            code=code,
                            description=description,
                            status="pending",
                            timestamp=datetime.now()
                        )
                        dtcs.append(dtc)
            except:
                pass  # Some vehicles don't support pending DTCs

            # Try to read permanent DTCs if supported
            try:
                permanent_cmd = obd.commands.GET_PERMANENT_DTC
                permanent_response = self.connection.query(permanent_cmd)

                if permanent_response.value:
                    for dtc_tuple in permanent_response.value:
                        code, description = dtc_tuple
                        dtc = DTCResult(
                            code=code,
                            description=description,
                            status="permanent",
                            timestamp=datetime.now()
                        )
                        dtcs.append(dtc)
            except:
                pass  # Some vehicles don't support permanent DTCs

        except Exception as e:
            logger.error(f"Error reading DTCs: {e}")

        return dtcs

    async def read_dtcs_enhanced(self) -> List[DTCResult]:
        """
        Enhanced DTC reading with response processing
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")

        dtcs = []
        current_protocol = self.protocol_manager.get_current_protocol()

        try:
            # Read stored DTCs with raw response processing
            cmd = obd.commands.GET_DTC
            response = self.connection.query(cmd)

            if response.raw:
                # Process raw response using our decoder
                dtc_codes = self.response_processor.process_dtc_response(response.raw, 0x03)

                for code in dtc_codes:
                    dtc = DTCResult(
                        code=code,
                        description=f"Diagnostic Trouble Code {code}",
                        status="stored",
                        timestamp=datetime.now()
                    )
                    dtcs.append(dtc)

            # Also use standard python-obd processing as fallback
            if response.value:
                for dtc_tuple in response.value:
                    code, description = dtc_tuple
                    # Avoid duplicates
                    if not any(dtc.code == code for dtc in dtcs):
                        dtc = DTCResult(
                            code=code,
                            description=description,
                            status="stored",
                            timestamp=datetime.now()
                        )
                        dtcs.append(dtc)

        except Exception as e:
            logger.error(f"Error in enhanced DTC reading: {e}")
            # Fallback to standard method
            return await self.read_dtcs()

        return dtcs
    
    async def clear_dtcs(self) -> bool:
        """
        Clear all stored DTCs
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")
        
        try:
            cmd = obd.commands.CLEAR_DTC
            response = self.connection.query(cmd)
            
            if response.is_null():
                logger.error("Failed to clear DTCs")
                return False
            
            logger.info("DTCs cleared successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing DTCs: {e}")
            return False
    
    async def read_parameter(self, pid: str) -> Optional[OBDParameter]:
        """
        Read a specific OBD parameter by PID
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")
        
        try:
            # Get command by PID
            cmd = obd.commands.has_pid(pid)
            if not cmd:
                logger.warning(f"PID {pid} not supported")
                return None
            
            response = self.connection.query(cmd)
            
            if response.value is not None:
                return OBDParameter(
                    pid=pid,
                    name=cmd.desc,
                    value=response.value.magnitude if hasattr(response.value, 'magnitude') else response.value,
                    unit=str(response.value.units) if hasattr(response.value, 'units') else "",
                    timestamp=datetime.now()
                )
            
        except Exception as e:
            logger.error(f"Error reading parameter {pid}: {e}")
        
        return None
    
    async def read_multiple_parameters(self, pids: List[str]) -> List[OBDParameter]:
        """
        Read multiple OBD parameters
        """
        parameters = []
        
        for pid in pids:
            param = await self.read_parameter(pid)
            if param:
                parameters.append(param)
        
        return parameters
    
    async def get_vehicle_info(self) -> Dict[str, Any]:
        """
        Get basic vehicle information
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")
        
        info = {}
        
        try:
            # VIN
            try:
                vin_cmd = obd.commands.VIN
                vin_response = self.connection.query(vin_cmd)
                if vin_response.value:
                    info['vin'] = vin_response.value
            except:
                pass
            
            # ECU Name
            try:
                ecu_cmd = obd.commands.ELM_ECU_NAME
                ecu_response = self.connection.query(ecu_cmd)
                if ecu_response.value:
                    info['ecu_name'] = ecu_response.value
            except:
                pass
            
            # Protocol
            info['protocol'] = str(self.connection.protocol_name())
            
            # Supported commands
            info['supported_commands'] = len(self.connection.supported_commands)
            
        except Exception as e:
            logger.error(f"Error getting vehicle info: {e}")
        
        return info
    
    async def get_freeze_frame_data(self, dtc_code: str) -> Optional[Dict[str, Any]]:
        """
        Get freeze frame data for a specific DTC
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")
        
        try:
            # Read freeze frame data
            cmd = obd.commands.GET_FREEZE_DTC
            response = self.connection.query(cmd)
            
            if response.value:
                # Parse freeze frame data
                freeze_data = {}
                # Implementation depends on the specific format returned
                # This is a simplified version
                freeze_data['dtc'] = dtc_code
                freeze_data['data'] = str(response.value)
                freeze_data['timestamp'] = datetime.now()
                
                return freeze_data
                
        except Exception as e:
            logger.error(f"Error getting freeze frame data for {dtc_code}: {e}")
        
        return None
    
    async def monitor_real_time(self, pids: List[str], duration: int = 60) -> List[OBDParameter]:
        """
        Monitor real-time data for specified duration
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")
        
        readings = []
        start_time = datetime.now()
        
        while (datetime.now() - start_time).seconds < duration:
            for pid in pids:
                param = await self.read_parameter(pid)
                if param:
                    readings.append(param)
            
            await asyncio.sleep(1)  # 1 second interval
        
        return readings

    def get_detected_vehicle(self) -> Optional[VehicleInfo]:
        """Get detected vehicle information"""
        return self.detected_vehicle

    async def detect_vehicle_info(self) -> Optional[VehicleInfo]:
        """Manually trigger vehicle detection"""
        if not self.is_connected:
            raise ConnectionError("Not connected to OBD2 adapter")

        try:
            self.detected_vehicle = await self.vehicle_detector.detect_vehicle(self.connection)
            return self.detected_vehicle
        except Exception as e:
            logger.error(f"Vehicle detection failed: {e}")
            return None
