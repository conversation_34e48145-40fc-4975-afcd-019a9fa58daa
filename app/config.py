"""
Configuration management for OBD2 AI Diagnostic System
"""
import os
from typing import Optional
try:
    from pydantic_settings import BaseSettings
except ImportError:
    try:
        from pydantic import BaseSettings
    except ImportError:
        # Fallback for development
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)
try:
    from pydantic import Field, ConfigDict
except ImportError:
    # Fallback for development
    def Field(default=None, **kwargs):
        return default

    class ConfigDict:
        def __init__(self, **kwargs):
            pass


class Settings(BaseSettings):
    """Application settings"""
    
    # API Configuration
    api_title: str = "OBD2 AI Diagnostic System"
    api_version: str = "1.0.0"
    api_description: str = "AI-powered OBD2 diagnostic system with brand-specific solutions"
    debug: bool = Field(default=False, env="DEBUG")
    
    # Database Configuration
    database_url: str = Field(default="sqlite:///./obd_diagnostics.db", env="DATABASE_URL")
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    
    # OBD2 Configuration
    obd_port: Optional[str] = Field(default=None, env="OBD_PORT")
    obd_baudrate: int = Field(default=38400, env="OBD_BAUDRATE")
    obd_timeout: float = Field(default=30.0, env="OBD_TIMEOUT")
    can_interface: str = Field(default="can0", env="CAN_INTERFACE")
    can_bitrate: int = Field(default=500000, env="CAN_BITRATE")
    
    # AI Configuration
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-3.5-turbo", env="OPENAI_MODEL")
    use_local_llm: bool = Field(default=False, env="USE_LOCAL_LLM")
    local_llm_model_path: Optional[str] = Field(default=None, env="LOCAL_LLM_MODEL_PATH")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="logs/obd_diagnostics.log", env="LOG_FILE")
    
    # Security
    secret_key: str = Field(default="your-secret-key-change-this", env="SECRET_KEY")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # External APIs
    vehicle_api_key: Optional[str] = Field(default=None, env="VEHICLE_API_KEY")
    
    model_config = ConfigDict(env_file=".env", extra="ignore")


# Global settings instance
settings = Settings()


class OBDConfig:
    """OBD2 specific configuration constants"""
    
    # Standard OBD2 PIDs
    STANDARD_PIDS = {
        0x00: "PIDs supported [01 - 20]",
        0x01: "Monitor status since DTCs cleared",
        0x02: "Freeze DTC",
        0x03: "Fuel system status",
        0x04: "Calculated engine load",
        0x05: "Engine coolant temperature",
        0x06: "Short term fuel trim—Bank 1",
        0x07: "Long term fuel trim—Bank 1",
        0x08: "Short term fuel trim—Bank 2",
        0x09: "Long term fuel trim—Bank 2",
        0x0A: "Fuel pressure",
        0x0B: "Intake manifold absolute pressure",
        0x0C: "Engine RPM",
        0x0D: "Vehicle speed",
        0x0E: "Timing advance",
        0x0F: "Intake air temperature",
        0x10: "MAF air flow rate",
        0x11: "Throttle position",
        0x12: "Commanded secondary air status",
        0x13: "Oxygen sensors present (in 2 banks)",
        0x14: "Oxygen Sensor 1 - Voltage, Short term fuel trim",
        0x15: "Oxygen Sensor 2 - Voltage, Short term fuel trim",
        0x16: "Oxygen Sensor 3 - Voltage, Short term fuel trim",
        0x17: "Oxygen Sensor 4 - Voltage, Short term fuel trim",
        0x18: "Oxygen Sensor 5 - Voltage, Short term fuel trim",
        0x19: "Oxygen Sensor 6 - Voltage, Short term fuel trim",
        0x1A: "Oxygen Sensor 7 - Voltage, Short term fuel trim",
        0x1B: "Oxygen Sensor 8 - Voltage, Short term fuel trim",
        0x1C: "OBD standards this vehicle conforms to",
        0x1D: "Oxygen sensors present (in 4 banks)",
        0x1E: "Auxiliary input status",
        0x1F: "Run time since engine start",
    }
    
    # OBD2 Modes
    MODES = {
        0x01: "Show current data",
        0x02: "Show freeze frame data",
        0x03: "Show stored Diagnostic Trouble Codes",
        0x04: "Clear Diagnostic Trouble Codes and stored values",
        0x05: "Test results, oxygen sensor monitoring",
        0x06: "Test results, other component/system monitoring",
        0x07: "Show pending Diagnostic Trouble Codes",
        0x08: "Control operation of on-board component/system",
        0x09: "Request vehicle information",
        0x0A: "Permanent Diagnostic Trouble Codes"
    }
    
    # DTC Prefixes
    DTC_PREFIXES = {
        'P': 'Powertrain',
        'B': 'Body',
        'C': 'Chassis',
        'U': 'Network/Communication'
    }


class CANConfig:
    """CAN bus configuration constants"""
    
    # Standard CAN IDs for different ECUs
    ENGINE_ECU_ID = 0x7E0
    TRANSMISSION_ECU_ID = 0x7E1
    ABS_ECU_ID = 0x7E2
    AIRBAG_ECU_ID = 0x7E3
    
    # UDS Service IDs
    UDS_SERVICES = {
        0x10: "Diagnostic Session Control",
        0x11: "ECU Reset",
        0x14: "Clear Diagnostic Information",
        0x19: "Read DTC Information",
        0x22: "Read Data By Identifier",
        0x23: "Read Memory By Address",
        0x27: "Security Access",
        0x28: "Communication Control",
        0x2E: "Write Data By Identifier",
        0x31: "Routine Control",
        0x34: "Request Download",
        0x35: "Request Upload",
        0x36: "Transfer Data",
        0x37: "Request Transfer Exit",
        0x3E: "Tester Present"
    }
