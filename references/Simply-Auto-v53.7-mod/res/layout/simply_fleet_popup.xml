      �  $          �                 #   -   2   ?   O   ^   n   �   �   �   �   �   �   �   �        3  B  W  q  �  �  �  �  �    &  7  �  �  �  �  textSize 		textStyle 		textColor gravity id 

background 

paddingBottom layout_width 

layout_height layout_marginLeft layout_marginTop layout_marginRight src text layout_toLeftOf layout_toRightOf layout_below layout_alignBottom layout_alignParentLeft layout_centerHorizontal CHECK IT
OUT Create Work Orders Driver and Admin Logins Get Running Cost of Your Fleet I DON'T
NEED THIS I'LL CHECK
LATER 		ImageView ##Log Fill-ups, Services and Expenses Maintain Service Schedules Perform Vehicle Inspections RelativeLayout ����Simply Fleet is an app designed for fleets of all sizes. It is an affordable and efficient way to track your fleet's performance. TextView Track Daily Driver Mileage android **http://schemas.android.com/apk/res/android � X   � � � � � � � � � � � � O������        ����"   #    t      ��������            #      ����  �#      ����    #      ����  ����#      ����  ���� �   	   ��������           #      ����  J	#      ����  }  #      ����  }  #   
   ����  
  #      ����  � #      ����  ����    	   ��������    �      ��������      	      #       ����    #      ����  � #      ����  �	#      ����  ����#      ����  ����#   	   ����    #      ����    #   
           #      ����  J	       ��������     �      ��������           #      ����  � 	#      ����  ����#      ����  ����#   	   ����    #   
   ����    #      ����  � #      ����  �	#      ����  ����       ��������    �   &   ��������      
      #       ����    #      ����  � #      ����  %	#      ����    #      ����  ����#      ����  ����#   	   ����  
  #   
           #      ����  � 	#      ����  � 	    &   ��������     �   2   ��������           #      ����  � 	#      ����  ����#      ����  ����#   	   ����    #   
   ����    #      ����  � #      ����  � 	#      ����  ����    2   ��������    �   <   ��������      
      #       ����    #      ����  � #      ����  &	#      ����    #      ����  ����#      ����  ����#   	   ����  
  #   
           #      ����  � 	#      ����  � 	    <   ��������     �   H   ��������           #      ����  � 	#      ����  ����#      ����  ����#   	   ����    #   
   ����    #      ����  � #      ����  � 	#      ����  ����    H   ��������    �   R   ��������      
      #       ����    #      ����  � #      ����  '	#      ����    #      ����  ����#      ����  ����#   	   ����  
  #   
   !     !   #      ����  � 	#      ����  � 	    R   ��������     �   ^   ��������           #      ����  � 	#      ����  ����#      ����  ����#   	   ����    #   
   ����    #      ����  � #      ����  � 	#      ����  ����    ^   ��������    �   h   ��������      
      #       ����    #      ����  � #      ����  (	#      ����    #      ����  ����#      ����  ����#   	   ����  
  #   
           #      ����  � 	#      ����  � 	    h   ��������     �   t   ��������           #      ����  � 	#      ����  ����#      ����  ����#   	   ����    #   
   ����    #      ����  � #      ����  � 	#      ����  ����    t   ��������    �   ~   ��������      
      #       ����    #      ����  � #      ����  )	#      ����    #      ����  ����#      ����  ����#   	   ����  
  #   
           #      ����  � 	#      ����  � 	    ~   ��������     �   �   ��������           #      ����  � 	#      ����  ����#      ����  ����#   	   ����    #   
   ����    #      ����  � #      ����  � 	#      ����  ����    �   ��������    �   �   ��������      
      #       ����    #      ����  � #      ����  *	#      ����    #      ����  ����#      ����  ����#   	   ����  
  #   
           #      ����  � 	#      ����  � 	    �   ��������     �   �   ��������           #      ����  � 	#      ����  ����#      ����  ����#   	   ����    #   
   ����    #      ����  � #      ����  � 	#      ����  ����    �   ��������    �   �   ��������      
      #       ����    #      ����  � #      ����  +	#      ����    #      ����  ����#      ����  ����#   	   ����  
  #   
           #      ����  � 	#      ����  � 	    �   ��������     �   �   ��������            #      ����     #      ����  ����#      ����  ����#   	   ����    #   
   ����    #      ����    #      ����  � 	 �   �   ��������            #       ����    #      ����     #      ����  �#      ����  � 	#      ����  ����#      ����  ����#   
               �   ��������     �   �   ��������      	      #       ����    #      ����     #      ����  �#      ����  � 	#      ����  ����#      ����  ����#      ����    #   
           #      ����  � 	    �   ��������     �   �   ��������      	      #       ����    #      ����     #      ����  �#      ����  � 	#      ����  ����#      ����  ����#      ����    #   
           #      ����  � 	    �   ��������        �   ��������          ��������          ����"   #   